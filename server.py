import os
import random
import string
import threading
from flask import Flask, send_from_directory, send_file, request
from flask_socketio import <PERSON><PERSON><PERSON>, emit, join_room, leave_room
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Create Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = 'chess_secret_key'

# Create Socket.IO server
socketio = SocketIO(app, cors_allowed_origins="*")

# Game rooms storage
game_rooms = {}

# Global waiting queue for matchmaking
waiting_players = []

# Thread lock for thread-safe operations
lock = threading.Lock()

def generate_room_id():
    """Generate a random room ID"""
    return ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))

# Serve static files
@app.route('/')
def index():
    return send_file('chess.html')

@app.route('/<path:filename>')
def static_files(filename):
    return send_from_directory('.', filename)

# Socket.IO event handlers
@socketio.on('connect')
def handle_connect():
    print(f'A user connected: {request.sid}')
    emit('connected', {'data': 'Connected to server'})

@socketio.on('joinGame')
def handle_join_game():
    """Handle player joining the game queue"""
    sid = request.sid

    with lock:
        global waiting_players

        # Check if player is already in waiting queue
        if any(p['id'] == sid for p in waiting_players):
            return  # Player already in queue

        # Add player to waiting queue
        waiting_players.append({'id': sid, 'ready': True})

        print(f'Player joined waiting queue: {sid}')

        # Broadcast updated waiting players list to all waiting players
        emit('waitingPlayersUpdate', {'players': waiting_players}, broadcast=True)

        # Check if we have at least 2 players to start a game
        if len(waiting_players) >= 2:
            # Take the first two players
            player1 = waiting_players[0]
            player2 = waiting_players[1]

            # Create a new game room
            room_id = generate_room_id()
            game_rooms[room_id] = {
                'players': [
                    {'id': player1['id'], 'color': 'w', 'name': 'White Player'},
                    {'id': player2['id'], 'color': 'b', 'name': 'Black Player'}
                ],
                'currentPlayer': 0,  # White starts first
                'gameStarted': True,
                'gameEnded': False,
                'fen': 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'  # Starting position
            }

            # Join both players to the room
            join_room(room_id, sid=player1['id'])
            join_room(room_id, sid=player2['id'])

            # Remove players from waiting queue
            waiting_players = [p for p in waiting_players if p['id'] not in [player1['id'], player2['id']]]

            # Broadcast updated waiting players list
            emit('waitingPlayersUpdate', {'players': waiting_players}, broadcast=True)

            # Start the game for both players
            emit('gameStart', {
                'currentPlayer': 0,
                'players': game_rooms[room_id]['players']
            }, room=room_id)

            print(f'Chess game started in room: {room_id} with players: {player1["id"]} (White) and {player2["id"]} (Black)')

@socketio.on('makeMove')
def handle_make_move(data):
    """Handle player making a move"""
    sid = request.sid

    with lock:
        # Find the room this player is in
        room_id = None
        for rid, room in game_rooms.items():
            if any(p['id'] == sid for p in room['players']):
                room_id = rid
                break

        if not room_id or room_id not in game_rooms:
            return

        room = game_rooms[room_id]

        if not room['gameStarted'] or room['gameEnded']:
            return

        # Find player index
        player_index = None
        for i, player in enumerate(room['players']):
            if player['id'] == sid:
                player_index = i
                break

        # Check if it's this player's turn
        if player_index != room['currentPlayer']:
            emit('error', {'message': 'Not your turn'})
            return

        # Update game state
        room['fen'] = data['fen']

        # Switch to next player
        room['currentPlayer'] = 1 if room['currentPlayer'] == 0 else 0

        # Send move to opponent
        emit('moveReceived', {
            'move': data['move'],
            'fen': data['fen'],
            'currentPlayer': room['currentPlayer']
        }, room=room_id, include_self=False)

        print(f'Move made in room {room_id}: {data["move"]["san"]}')

@socketio.on('resign')
def handle_resign():
    """Handle player resignation"""
    sid = request.sid

    with lock:
        # Find the room this player is in
        room_id = None
        for rid, room in game_rooms.items():
            if any(p['id'] == sid for p in room['players']):
                room_id = rid
                break

        if not room_id or room_id not in game_rooms:
            return

        room = game_rooms[room_id]

        if not room['gameStarted'] or room['gameEnded']:
            return

        room['gameEnded'] = True

        # Notify opponent
        emit('opponentResigned', room=room_id, include_self=False)

        print(f'Player resigned in room: {room_id}')

@socketio.on('timeOut')
def handle_timeout(data):
    """Handle player timeout"""
    sid = request.sid

    with lock:
        # Find the room this player is in
        room_id = None
        for rid, room in game_rooms.items():
            if any(p['id'] == sid for p in room['players']):
                room_id = rid
                break

        if not room_id or room_id not in game_rooms:
            return

        room = game_rooms[room_id]

        if not room['gameStarted'] or room['gameEnded']:
            return

        room['gameEnded'] = True

        # Notify opponent
        emit('opponentTimeOut', {'side': data['side']}, room=room_id, include_self=False)

        print(f'Player timed out in room: {room_id}, side: {data["side"]}')

@socketio.on('disconnect')
def handle_disconnect():
    sid = request.sid
    print(f'User disconnected: {sid}')

    with lock:
        # Check if player was in waiting queue
        global waiting_players
        player_in_queue = any(p['id'] == sid for p in waiting_players)

        if player_in_queue:
            # Remove player from waiting queue
            waiting_players = [p for p in waiting_players if p['id'] != sid]

            # Broadcast updated waiting players list
            emit('waitingPlayersUpdate', {'players': waiting_players}, broadcast=True)

            print(f'Player removed from waiting queue: {sid}')
            return

        # Handle disconnect from game room
        room_id = None
        for rid, room in game_rooms.items():
            if any(p['id'] == sid for p in room['players']):
                room_id = rid
                break

        if room_id and room_id in game_rooms:
            room = game_rooms[room_id]

            # Notify opponent about disconnection
            emit('opponentDisconnected', room=room_id, include_self=False)

            # End the game
            if not room['gameEnded']:
                room['gameEnded'] = True

            # Clean up room after a delay
            def cleanup_room():
                threading.Timer(30.0, lambda: game_rooms.pop(room_id, None)).start()
                print(f'Room deleted: {room_id}')

            cleanup_room()

if __name__ == '__main__':
    PORT = int(os.environ.get('PORT', 3000))
    print(f'Starting Chess server on port {PORT}')

    try:
        # Run the Flask app with Socket.IO
        socketio.run(app, host='0.0.0.0', port=PORT, debug=True)
    except Exception as e:
        print(f'Error starting server: {e}')
        import traceback
        traceback.print_exc()
