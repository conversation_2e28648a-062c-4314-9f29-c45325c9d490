#!/usr/bin/env python3
"""
Minimal Flask-SocketIO test server
"""

from flask import Flask
from flask_socketio import Socket<PERSON>, emit

# Create Flask app
app = Flask(__name__)

# Create Socket.IO server
socketio = SocketIO(app, cors_allowed_origins="*")

@app.route('/')
def index():
    return '<h1>Test Server Running</h1><script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script><script>const socket = io(); socket.on("connect", () => console.log("Connected!")); socket.on("test", (data) => console.log("Received:", data));</script>'

@socketio.on('connect')
def handle_connect():
    print(f'Client connected')
    emit('test', {'message': 'Hello from server!'})

@socketio.on('disconnect')
def handle_disconnect():
    print('Client disconnected')

if __name__ == '__main__':
    print('Starting test server on port 3000...')
    socketio.run(app, host='0.0.0.0', port=3000, debug=True)
