/**
 * Chess Game - Socket.IO Multiplayer Matchmaking
 * Includes checkmate, stalemate, check, castling, en passant, and promotion
 *
 * Castling is fully supported through the chess.js library and the UI will
 * properly highlight the king and rook movement during castling moves.
 */
document.addEventListener('DOMContentLoaded', () => {
    // DOM Elements
    const matchmaking = document.getElementById('matchmaking');
    const gameMode = document.getElementById('game');
    const matchmakingMessage = document.getElementById('matchmaking-message');
    const playerCountElement = document.getElementById('player-count');
    const promotionPopup = document.getElementById('promotion-popup');

    // Mobile detection
    const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    console.log("Mobile device detected:", isMobile);

    // Selected piece tracking for mobile tap interface
    let selectedSquare = null;
    let selectedPiece = null;

    // Game Elements
    const statusElement = document.getElementById('status-message');
    const playerInfoElement = document.getElementById('player-info');
    const playerColorElement = document.getElementById('player-color');
    const moveListElement = document.getElementById('move-list');
    const whiteCapturedElement = document.getElementById('white-captured');
    const blackCapturedElement = document.getElementById('black-captured');


    // Timer Elements
    const whiteTimerElement = document.getElementById('white-timer');
    const blackTimerElement = document.getElementById('black-timer');

    // Game state variables
    let game = new Chess();
    let board = null;
    let socket = null;
    let playerSide = 'w';
    let capturedPieces = { w: [], b: [] };
    let $boardEl = null;
    let lastMoveSource = null;
    let lastMoveTarget = null;
    let gameStartTime = null;
    let gameEndTime = null;
    let waitingForOpponent = true;
    let playersOnline = 0;
    let moveHistory = [];
    let isPlayerTurn = false;
    
    // Promotion variables
    let pendingPromotion = null;
    let pendingPromotionSource = null;
    let pendingPromotionTarget = null;

    // Timer variables
    let whiteTimeLeft = 600; // 10 minutes in seconds
    let blackTimeLeft = 600; // 10 minutes in seconds
    let timerInterval = null;
    let currentTimerSide = 'w'; // Which side's timer is currently running

    // Piece theme config
    const pieceTheme = (piece) => {
        return `https://chessboardjs.com/img/chesspieces/alpha/${piece}.png`;
    };

    // Initialize Socket.IO connection
    initializeSocket();

    // Popup event listeners
    const gameOverPopup = document.getElementById('game-over-popup');

    // Close popup when clicking anywhere on it
    gameOverPopup.addEventListener('click', () => {
        hideGameOverPopup();
    });
    
    // Setup promotion popup event listeners
    setupPromotionPopup();



    // ===== SOCKET.IO INITIALIZATION =====

    function initializeSocket() {
        socket = io();

        socket.on('connect', () => {
            console.log('Connected to server');
            startMatchmaking();
        });

        socket.on('disconnect', () => {
            console.log('Disconnected from server');
            matchmakingMessage.textContent = 'Connection lost. Reconnecting...';
        });

        socket.on('waitingPlayersUpdate', (data) => {
            playerCountElement.textContent = data.players.length;
        });

        socket.on('gameStart', (data) => {
            console.log('Game starting:', data);
            // Determine player side based on position in players array
            const mySocketId = socket.id;
            const myPlayerIndex = data.players.findIndex(p => p.id === mySocketId);
            playerSide = myPlayerIndex === 0 ? 'w' : 'b';
            isPlayerTurn = playerSide === 'w'; // White goes first

            console.log('Game started - Player side:', playerSide, 'Is my turn:', isPlayerTurn);
            startGame();
        });

        socket.on('moveReceived', (data) => {
            handleOpponentMove(data);
        });

        socket.on('opponentResigned', () => {
            stopTimer();
            statusElement.textContent = 'Opponent resigned. You win!';
            showGameOverPopup('You win by resignation!', 'resignation');
        });

        socket.on('opponentDisconnected', () => {
            statusElement.textContent = 'Opponent disconnected.';
            setTimeout(() => {
                if (confirm('Opponent disconnected. Start new game?')) {
                    startMatchmaking();
                }
            }, 2000);
        });

        socket.on('opponentTimeOut', (data) => {
            stopTimer();
            const winner = data.side === 'w' ? 'Black' : 'White';
            const loser = data.side === 'w' ? 'White' : 'Black';
            statusElement.textContent = `${loser} ran out of time! ${winner} wins!`;
            showGameOverPopup(`You win! ${loser} ran out of time.`, 'timeout');
        });
    }

    function startMatchmaking() {
        // Stop any running timer
        stopTimer();

        // Show matchmaking screen
        document.getElementById('matchmaking').style.display = 'block';
        document.getElementById('game').style.display = 'none';

        // Update matchmaking message
        matchmakingMessage.textContent = 'Searching for players...';

        // Join the matchmaking queue
        if (socket) {
            socket.emit('joinGame');
        }
    }

    function handleOpponentMove(data) {
        console.log('Received opponent move:', data);

        // Apply the move to our game
        const move = game.move(data.move);
        if (move) {
            board.position(game.fen());
            checkForCapture(move);
            highlightLastMove(data.move.from, data.move.to);
            addMoveToHistory(move);

            // Switch timer to our side
            switchTimer();

            // Now it's our turn
            isPlayerTurn = true;
            console.log('Opponent moved, now it\'s our turn. isPlayerTurn:', isPlayerTurn);

            updateGameStatus();
            updateTurnIndicator();
            
            // If the opponent made a promotion move, show it in the status
        if (move.promotion) {
            const promotedTo = getPieceName(move.promotion);
            updateStatus(`Opponent promoted pawn to ${promotedTo}`);
        }
        }
    }

    // Setup mobile tap handling
    // ===== DRAG AND DROP HANDLERS =====

    function onDragStart(source, piece, position, orientation) {
        console.log('Drag start:', source, piece, 'Game turn:', game.turn(), 'Player side:', playerSide, 'Is player turn:', isPlayerTurn);

        // Only allow dragging if it's the player's turn and it's their piece
        if (game.turn() !== playerSide || !isPlayerTurn) {
            console.log('Not player turn, blocking drag');
            return false;
        }

        // Only allow dragging pieces of the current player's color
        if ((game.turn() === 'w' && piece.search(/^b/) !== -1) ||
            (game.turn() === 'b' && piece.search(/^w/) !== -1)) {
            console.log('Wrong piece color, blocking drag');
            return false;
        }

        // Don't allow dragging if game is over
        if (game.game_over()) {
            console.log('Game over, blocking drag');
            return false;
        }

        // Highlight valid moves when dragging starts
        removeHighlighting();
        highlightValidMoves(source);

        return true;
    }

    function onDrop(source, target) {
        removeHighlighting();

        console.log('Drop attempt:', source, 'to', target);

        // Check if this is a pawn promotion move
        const piece = game.get(source);
        const isPromotion = isPawnPromotion(source, target);

        if (isPromotion) {
            // Store the pending promotion
            pendingPromotionSource = source;
            pendingPromotionTarget = target;
            pendingPromotion = true;
            
            // Show promotion popup
            showPromotionPopup(piece.color);
            
            // Position the board to show the pawn at the target position
            // This creates a visual effect of the pawn reaching the promotion square
            const tempPosition = { ...board.position() };
            delete tempPosition[source];
            tempPosition[target] = piece.color + 'P';
            board.position(tempPosition, false);
            
            // Return without making the move yet
            return;
        }

        // Make the move (non-promotion case)
        const move = game.move({
            from: source,
            to: target,
            promotion: 'q' // Default promotion piece if not handled by popup
        });

        // Illegal move
        if (move === null) {
            console.log('Illegal move, snapping back');
            return 'snapback';
        }

        console.log('Legal move made:', move);

        // Legal move - send to opponent
        if (socket) {
            socket.emit('makeMove', {
                move: move,
                fen: game.fen()
            });
        }

        checkForCapture(move);
        highlightLastMove(source, target);
        addMoveToHistory(move);

        // Switch timer to opponent
        switchTimer();

        // Now it's opponent's turn
        isPlayerTurn = false;
        console.log('We moved, now it\'s opponent\'s turn. isPlayerTurn:', isPlayerTurn);

        updateGameStatus();
        updateTurnIndicator();
    }

    function onSnapEnd() {
        board.position(game.fen());
    }

    function setupClickHandling() {
        // Clear any previous handlers
        $('#game-board').off('click', '.square-55d63');
        $('#game-board').off('click', '.piece-417db');

        // Add click event for squares
        $('#game-board').on('click', '.square-55d63', function(e) {
            // Prevent event bubbling
            e.stopPropagation();

            const square = $(this).attr('data-square');
            console.log("Square clicked:", square);
            if (game.turn() === playerSide && isPlayerTurn) {
                handleBoardClick(square);
            }
        });

        // Add click event specifically for pieces (for mobile)
        $('#game-board').on('click', '.piece-417db', function(e) {
            // Prevent event bubbling
            e.stopPropagation();

            // Get the square from the parent square element
            const square = $(this).closest('.square-55d63').attr('data-square');
            console.log("Piece clicked on square:", square);
            if (game.turn() === playerSide && isPlayerTurn) {
                handleBoardClick(square);
            }
        });

        // Add touch events for better mobile support
        $('#game-board').on('touchend', '.square-55d63, .piece-417db', function(e) {
            e.preventDefault();
            e.stopPropagation();

            let square;
            if ($(this).hasClass('piece-417db')) {
                square = $(this).closest('.square-55d63').attr('data-square');
            } else {
                square = $(this).attr('data-square');
            }

            console.log("Touch on square:", square);
            if (game.turn() === playerSide && isPlayerTurn) {
                handleBoardClick(square);
            }
        });
    }

    function startGame() {
        // Hide matchmaking, show game
        document.getElementById('matchmaking').style.display = 'none';
        document.getElementById('game').style.display = 'block';

        // Initialize the game
        game = new Chess();
        gameStartTime = new Date();
        gameEndTime = null;

        // Initialize timers
        initializeTimers();
        startTimer();

        // Initialize the board
        const boardConfig = {
            position: 'start',
            pieceTheme: pieceTheme,
            draggable: !isMobile, // Disable dragging on mobile, use click only
            sparePieces: false,
            showNotation: false,
            width: calculateBoardSize(),
            orientation: playerSide === 'w' ? 'white' : 'black',
            onDragStart: onDragStart,
            onDrop: onDrop,
            onSnapEnd: onSnapEnd
        };

        if (board) {
            board.destroy();
        }

        $boardEl = $('#game-board');
        board = Chessboard('game-board', boardConfig);

        // Update UI
        playerColorElement.textContent = playerSide === 'w' ? 'White' : 'Black';
        capturedPieces = { w: [], b: [] };
        updateCapturedPieces();
        updateGameStatus();
        updateTurnIndicator();

        // Setup click handling for both mobile and desktop
        setupClickHandling();

        // Clear move history
        moveHistory = [];
        moveListElement.innerHTML = '';

        // Resize handler
        $(window).resize(() => {
            if (board) {
                board.resize(calculateBoardSize());
            }
        });
    }

    // Handle board click for mobile devices
    function handleBoardClick(square) {
        // Only allow moves on player's turn
        if (game.turn() !== playerSide || !isPlayerTurn) return;

        const piece = game.get(square);

        if (selectedSquare === null) {
            // First click - select a piece
            if (piece && piece.color === playerSide) {
                selectedSquare = square;
                selectedPiece = piece;
                highlightSquare(square);
                highlightValidMoves(square);
            }
        } else {
            // Second click - try to move
            if (square === selectedSquare) {
                // Clicking same square - deselect
                selectedSquare = null;
                selectedPiece = null;
                removeHighlighting();
            } else {
                // Check if this is a pawn promotion move
                const isPromotion = isPawnPromotion(selectedSquare, square);

                if (isPromotion) {
                    // Store the pending promotion
                    pendingPromotionSource = selectedSquare;
                    pendingPromotionTarget = square;
                    pendingPromotion = true;
                    
                    // Show promotion popup
                    showPromotionPopup(selectedPiece.color);
                    
                    // Clear selection but don't make the move yet
                    removeHighlighting();
                    selectedSquare = null;
                    selectedPiece = null;
                    return;
                }

                // Try to make the move
                const move = game.move({
                    from: selectedSquare,
                    to: square,
                    promotion: 'q'
                });

                if (move) {
                    // Valid move - send to opponent
                    board.position(game.fen());

                    if (socket) {
                        socket.emit('makeMove', {
                            move: move,
                            fen: game.fen()
                        });
                    }

                    checkForCapture(move);
                    highlightLastMove(selectedSquare, square);
                    addMoveToHistory(move);

                    // Switch timer to opponent
                    switchTimer();

                    updateGameStatus();
                    updateTurnIndicator();
                    isPlayerTurn = false; // Now it's opponent's turn

                    // Clear selection
                    selectedSquare = null;
                    selectedPiece = null;
                } else {
                    // Invalid move - try to select new piece
                    if (piece && piece.color === playerSide) {
                        selectedSquare = square;
                        selectedPiece = piece;
                        removeHighlighting();
                        highlightSquare(square);
                        highlightValidMoves(square);
                    } else {
                        // Clear selection
                        selectedSquare = null;
                        selectedPiece = null;
                        removeHighlighting();
                    }
                }
            }
        }
    }

    // Function to check if a move is a pawn promotion
    function isPawnPromotion(source, target) {
        const piece = game.get(source);
        if (!piece || piece.type !== 'p') return false;
        
        // Check if pawn is moving to the last rank
        const targetRank = target.charAt(1);
        return (piece.color === 'w' && targetRank === '8') || 
               (piece.color === 'b' && targetRank === '1');
    }
    
    // Function to show the promotion popup
    function showPromotionPopup(color) {
        promotionPopup.style.display = 'flex';
        
        // Clear previous content
        promotionPopup.innerHTML = '';
        
        // Create title
        const title = document.createElement('div');
        title.className = 'promotion-title';
        title.textContent = '';
        promotionPopup.appendChild(title);
        
        // Create promotion options container
        const optionsContainer = document.createElement('div');
        optionsContainer.className = 'promotion-options';
        promotionPopup.appendChild(optionsContainer);
        
        // Create promotion options
        const pieces = ['q', 'r', 'n', 'b'];
        const pieceNames = ['Queen', 'Rook', 'Knight', 'Bishop'];
        
        pieces.forEach((piece, index) => {
            const pieceElement = document.createElement('div');
            pieceElement.className = 'promotion-piece';
            pieceElement.setAttribute('data-piece', piece);
            
            // Create piece image with improved styling
            const pieceImg = document.createElement('div');
            pieceImg.className = `promotion-piece-img ${color}${piece.toUpperCase()}`;
            pieceImg.style.backgroundImage = `url('https://chessboardjs.com/img/chesspieces/alpha/${color}${piece.toUpperCase()}.png')`;
            pieceElement.appendChild(pieceImg);
            
            // Create piece name
            const pieceName = document.createElement('div');
            pieceName.className = 'promotion-piece-name';
            pieceName.textContent = pieceNames[index];
            pieceElement.appendChild(pieceName);
            
            pieceElement.addEventListener('click', () => selectPromotionPiece(piece));
            optionsContainer.appendChild(pieceElement);
        });
        
        // Position the popup centered over the board for better visibility
        const boardElement = $('#game-board');
        if (boardElement.length) {
            const boardPos = boardElement.position();
            const boardWidth = boardElement.width();
            const boardHeight = boardElement.height();
            const popupWidth = 300; // Match the min-width in CSS
            
            // Center the popup over the board
            promotionPopup.style.position = 'absolute';
            promotionPopup.style.top = `${boardPos.top + (boardHeight / 2) - 150}px`;
            promotionPopup.style.left = `${boardPos.left + (boardWidth / 2) - (popupWidth / 2)}px`;
        }
    }
    
    // Function to handle promotion piece selection
    function selectPromotionPiece(piece) {
        promotionPopup.style.display = 'none';
        
        if (pendingPromotion && pendingPromotionSource && pendingPromotionTarget) {
            // Make the move with the selected promotion piece
            const move = game.move({
                from: pendingPromotionSource,
                to: pendingPromotionTarget,
                promotion: piece
            });
            
            if (move) {
                // Update the board to reflect the actual game state
                board.position(game.fen());
                
                if (socket) {
                    socket.emit('makeMove', {
                        move: move,
                        fen: game.fen()
                    });
                }
                
                checkForCapture(move);
                highlightLastMove(pendingPromotionSource, pendingPromotionTarget);
                addMoveToHistory(move);
                
                // Switch timer to opponent
                switchTimer();
                
                // Now it's opponent's turn
                isPlayerTurn = false;
                
                updateGameStatus();
                updateTurnIndicator();
            } else {
                // If move failed for some reason, restore the board to the correct position
                board.position(game.fen());
            }
            
            // Reset pending promotion
            pendingPromotion = null;
            pendingPromotionSource = null;
            pendingPromotionTarget = null;
        }
    }
    
    // Setup promotion popup event listeners
    function setupPromotionPopup() {
        // Close promotion popup when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target !== promotionPopup && !promotionPopup.contains(e.target) && 
                promotionPopup.style.display === 'flex') {
                promotionPopup.style.display = 'none';
                pendingPromotion = null;
                pendingPromotionSource = null;
                pendingPromotionTarget = null;
            }
        });
    }



    function updateTurnIndicator() {
        const turnIndicator = document.querySelector('.turn-indicator');
        const isMyTurn = game.turn() === playerSide && isPlayerTurn;

        console.log('Updating turn indicator:', {
            gameTurn: game.turn(),
            playerSide: playerSide,
            isPlayerTurn: isPlayerTurn,
            isMyTurn: isMyTurn
        });

        if (turnIndicator) {
            if (isMyTurn) {
                turnIndicator.textContent = 'Your Turn';
                turnIndicator.className = 'turn-indicator my-turn';
            } else {
                turnIndicator.textContent = "Opponent's Turn";
                turnIndicator.className = 'turn-indicator opponent-turn';
            }
        }
    }

    function removeHighlighting() {
        $boardEl.find('.square-55d63').removeClass('highlight-square');
        $boardEl.find('.square-55d63').removeClass('highlight-target');
        $boardEl.find('.square-55d63').removeClass('highlight-last-move');
        $boardEl.find('.square-55d63').removeClass('highlight-castle');
        // Don't remove check highlight here - it will be managed in updateGameStatus
    }

    function highlightSquare(square) {
        $boardEl.find(`.square-${square}`).addClass('highlight-square');
    }

    function highlightLastMove(source, target) {
        removeHighlighting();
        highlightSquare(source);
        highlightSquare(target);
        lastMoveSource = source;
        lastMoveTarget = target;
        
        // Check if this was a castling move
        const piece = game.get(target);
        if (piece && piece.type === 'k') {
            // Check for kingside castling
            if (source === 'e1' && target === 'g1') {
                // Highlight rook move from h1 to f1
                highlightSquare('h1');
                highlightSquare('f1');
            } else if (source === 'e8' && target === 'g8') {
                // Highlight rook move from h8 to f8
                highlightSquare('h8');
                highlightSquare('f8');
            }
            // Check for queenside castling
            else if (source === 'e1' && target === 'c1') {
                // Highlight rook move from a1 to d1
                highlightSquare('a1');
                highlightSquare('d1');
            } else if (source === 'e8' && target === 'c8') {
                // Highlight rook move from a8 to d8
                highlightSquare('a8');
                highlightSquare('d8');
            }
        }
    }

    function highlightValidMoves(square) {
        const moves = game.moves({ square: square, verbose: true });
        moves.forEach(move => {
            $boardEl.find(`.square-${move.to}`).addClass('highlight-target');
            
            // Special handling for castling moves
            if (move.san === 'O-O' || move.san === 'O-O-O') {
                // This is a castling move, highlight the rook's destination as well
                if (move.san === 'O-O') { // Kingside castling
                    if (game.turn() === 'w') {
                        $boardEl.find('.square-f1').addClass('highlight-castle');
                    } else {
                        $boardEl.find('.square-f8').addClass('highlight-castle');
                    }
                } else { // Queenside castling
                    if (game.turn() === 'w') {
                        $boardEl.find('.square-d1').addClass('highlight-castle');
                    } else {
                        $boardEl.find('.square-d8').addClass('highlight-castle');
                    }
                }
            }
        });
    }

    function checkForCapture(move) {
        if (move.captured) {
            const color = move.color === 'w' ? 'b' : 'w';
            capturedPieces[color].push(move.captured);
            updateCapturedPieces();
        }
    }

    function updateCapturedPieces() {
        whiteCapturedElement.innerHTML = '';
        blackCapturedElement.innerHTML = '';

        capturedPieces.b.forEach(piece => {
            const pieceIcon = getPieceIcon('b', piece);
            whiteCapturedElement.innerHTML += pieceIcon;
        });

        capturedPieces.w.forEach(piece => {
            const pieceIcon = getPieceIcon('w', piece);
            blackCapturedElement.innerHTML += pieceIcon;
        });
    }

    function getPieceIcon(color, piece) {
        const colorCode = color === 'w' ? 'white' : 'black';
        let pieceName = '';

        switch(piece) {
            case 'p': pieceName = 'pawn'; break;
            case 'n': pieceName = 'knight'; break;
            case 'b': pieceName = 'bishop'; break;
            case 'r': pieceName = 'rook'; break;
            case 'q': pieceName = 'queen'; break;
            case 'k': pieceName = 'king'; break;
        }

        return `<div class="captured-piece"><span class="piece-icon ${colorCode}-${piece}">&#${getPieceUnicode(color, piece)};</span></div>`;
    }

    function getPieceUnicode(color, piece) {
        const pieces = {
            'w': {
                'p': '9817',
                'n': '9816',
                'b': '9815',
                'r': '9814',
                'q': '9813',
                'k': '9812'
            },
            'b': {
                'p': '9823',
                'n': '9822',
                'b': '9821',
                'r': '9820',
                'q': '9819',
                'k': '9818'
            }
        };

        return pieces[color][piece];
    }
    
    // Function to get piece name for display
    function getPieceName(type) {
        const pieceNames = {
            'q': 'Queen',
            'r': 'Rook',
            'n': 'Knight',
            'b': 'Bishop',
            'p': 'Pawn',
            'k': 'King'
        };
        return pieceNames[type] || type;
    }

    function addMoveToHistory(move) {
        moveHistory.push(move);
        updateMoveList();
    }

    function updateMoveList() {
        moveListElement.innerHTML = '';

        for (let i = 0; i < moveHistory.length; i += 2) {
            const moveNumber = Math.floor(i / 2) + 1;
            const whiteMove = moveHistory[i] ? moveHistory[i].san : '';
            const blackMove = moveHistory[i + 1] ? moveHistory[i + 1].san : '';

            const movePair = document.createElement('div');
            movePair.className = 'move-pair';

            const moveNumElement = document.createElement('div');
            moveNumElement.className = 'move-number';
            moveNumElement.textContent = moveNumber + '.';

            const whiteMoveElement = document.createElement('div');
            whiteMoveElement.className = 'move-white';
            whiteMoveElement.textContent = whiteMove;

            const blackMoveElement = document.createElement('div');
            blackMoveElement.className = 'move-black';
            blackMoveElement.textContent = blackMove;

            movePair.appendChild(moveNumElement);
            movePair.appendChild(whiteMoveElement);
            movePair.appendChild(blackMoveElement);

            moveListElement.appendChild(movePair);
        }

        // Scroll to bottom of move list
        moveListElement.scrollTop = moveListElement.scrollHeight;
    }

    // Enhanced game status with proper chess rules
    function updateGameStatus() {
        let statusText = '';
        let gameOver = false;
        let result = '';
        let resultType = '';
        
        // Remove any previous check highlight
        $boardEl.find('.square-55d63').removeClass('highlight-check');

        if (game.in_checkmate()) {
            const winner = game.turn() === 'w' ? 'Black' : 'White';
            statusText = `Checkmate! ${winner} wins!`;
            result = `${winner} wins by checkmate!`;
            resultType = 'checkmate';
            gameOver = true;
            isPlayerTurn = false;
            
            // Highlight the king in checkmate
            highlightKingInCheck(game.turn());
        } else if (game.in_stalemate()) {
            statusText = 'Stalemate! Game is a draw.';
            result = 'Game drawn by stalemate';
            resultType = 'stalemate';
            gameOver = true;
            isPlayerTurn = false;
        } else if (game.in_threefold_repetition()) {
            statusText = 'Draw by threefold repetition.';
            result = 'Game drawn by threefold repetition';
            resultType = 'draw';
            gameOver = true;
            isPlayerTurn = false;
        } else if (game.insufficient_material()) {
            statusText = 'Draw by insufficient material.';
            result = 'Game drawn by insufficient material';
            resultType = 'draw';
            gameOver = true;
            isPlayerTurn = false;
        } else if (game.in_draw()) {
            statusText = 'Game is a draw.';
            result = 'Game is a draw';
            resultType = 'draw';
            gameOver = true;
            isPlayerTurn = false;
        } else if (game.in_check()) {
            const playerInCheck = game.turn() === 'w' ? 'White' : 'Black';
            statusText = `${playerInCheck} is in check!`;
            
            // Highlight the king in check
            highlightKingInCheck(game.turn());
        } else {
            const currentTurn = game.turn() === 'w' ? 'White' : 'Black';
            const isMyTurn = game.turn() === playerSide && isPlayerTurn;
            statusText = isMyTurn ? 'Your turn' : `${currentTurn} to move`;
        }

        updateStatus(statusText);

        if (gameOver) {
            gameEndTime = new Date();
            stopTimer();
            showGameOverPopup(result, resultType);
        }
    }

    // Popup functions
    function showGameOverPopup(result, resultType) {
        const popup = document.getElementById('game-over-popup');
        const titleElement = document.getElementById('game-result-title');
        const messageElement = document.getElementById('game-result-message');
        const iconElement = document.getElementById('result-icon');
        const resultIconContainer = document.querySelector('.result-icon');
        const totalMovesElement = document.getElementById('total-moves');
        const gameDurationElement = document.getElementById('game-duration');
        const finalResultElement = document.getElementById('final-result');

        // Set title and message
        titleElement.textContent = 'Game Over';
        messageElement.textContent = result;

        // Set icon based on result type
        resultIconContainer.className = `result-icon ${resultType}`;
        switch(resultType) {
            case 'checkmate':
                iconElement.className = 'fas fa-crown';
                break;
            case 'stalemate':
                iconElement.className = 'fas fa-handshake';
                break;
            case 'draw':
                iconElement.className = 'fas fa-balance-scale';
                break;
            case 'timeout':
                iconElement.className = 'fas fa-clock';
                break;
            default:
                iconElement.className = 'fas fa-flag';
        }

        // Calculate and display stats
        const totalMoves = Math.ceil(moveHistory.length / 2);
        totalMovesElement.textContent = totalMoves;

        const duration = gameEndTime && gameStartTime ?
            formatGameDuration(gameEndTime - gameStartTime) : '00:00';
        gameDurationElement.textContent = duration;

        finalResultElement.textContent = getResultText(resultType);

        // Show popup
        popup.classList.add('show');
    }

    function hideGameOverPopup() {
        const popup = document.getElementById('game-over-popup');
        popup.classList.remove('show');
    }

    function formatGameDuration(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const minutes = Math.floor(totalSeconds / 60);
        const seconds = totalSeconds % 60;
        return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    function getResultText(resultType) {
        switch(resultType) {
            case 'checkmate': return 'Checkmate';
            case 'stalemate': return 'Stalemate';
            case 'draw': return 'Draw';
            case 'resignation': return 'Resignation';
            case 'timeout': return 'Timeout';
            default: return 'Game Over';
        }
    }

    function updateStatus(message) {
        statusElement.textContent = message;
    }

    // Highlight the king that is in check
    function highlightKingInCheck(color) {
        // Find the king's position on the board
        const files = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h'];
        const ranks = ['1', '2', '3', '4', '5', '6', '7', '8'];
        
        // Iterate through all squares on the board
        for (let file of files) {
            for (let rank of ranks) {
                const square = file + rank;
                const piece = game.get(square);
                if (piece && piece.type === 'k' && piece.color === color) {
                    // Found the king, highlight its square
                    const squareEl = $boardEl.find('.square-' + square);
                    squareEl.addClass('highlight-check');
                    return;
                }
            }
        }
    }

    function calculateBoardSize() {
        const container = document.querySelector('.game-board-container');
        if (!container) return 400;

        const containerWidth = container.clientWidth;
        const maxSize = Math.min(containerWidth - 40, 500);
        return Math.max(maxSize, 300);
    }

    // ===== TIMER FUNCTIONS =====

    function initializeTimers() {
        whiteTimeLeft = 600; // 10 minutes
        blackTimeLeft = 600; // 10 minutes
        currentTimerSide = 'w'; // White starts first
        updateTimerDisplay();
        updateTimerStyles();
    }

    function startTimer() {
        if (timerInterval) {
            clearInterval(timerInterval);
        }

        timerInterval = setInterval(() => {
            if (currentTimerSide === 'w') {
                whiteTimeLeft--;
                if (whiteTimeLeft <= 0) {
                    whiteTimeLeft = 0;
                    handleTimeOut('w');
                    return;
                }
            } else {
                blackTimeLeft--;
                if (blackTimeLeft <= 0) {
                    blackTimeLeft = 0;
                    handleTimeOut('b');
                    return;
                }
            }

            updateTimerDisplay();
            updateTimerStyles();
        }, 1000);
    }

    function stopTimer() {
        if (timerInterval) {
            clearInterval(timerInterval);
            timerInterval = null;
        }
    }

    function switchTimer() {
        currentTimerSide = currentTimerSide === 'w' ? 'b' : 'w';
        updateTimerStyles();
    }

    function updateTimerDisplay() {
        whiteTimerElement.textContent = formatTime(whiteTimeLeft);
        blackTimerElement.textContent = formatTime(blackTimeLeft);
    }

    function updateTimerStyles() {
        const whiteTimerSection = whiteTimerElement.parentElement;
        const blackTimerSection = blackTimerElement.parentElement;

        // Remove all timer classes
        whiteTimerSection.classList.remove('active', 'warning', 'danger');
        blackTimerSection.classList.remove('active', 'warning', 'danger');

        // Add active class to current timer
        if (currentTimerSide === 'w') {
            whiteTimerSection.classList.add('active');
            addTimerWarningClass(whiteTimerSection, whiteTimeLeft);
        } else {
            blackTimerSection.classList.add('active');
            addTimerWarningClass(blackTimerSection, blackTimeLeft);
        }
    }

    function addTimerWarningClass(timerSection, timeLeft) {
        if (timeLeft <= 30) {
            timerSection.classList.add('danger');
        } else if (timeLeft <= 60) {
            timerSection.classList.add('warning');
        }
    }

    function formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    function handleTimeOut(side) {
        stopTimer();
        gameEndTime = new Date();
        isPlayerTurn = false;

        const winner = side === 'w' ? 'Black' : 'White';
        const loser = side === 'w' ? 'White' : 'Black';

        statusElement.textContent = `${loser} ran out of time! ${winner} wins!`;

        // Send timeout result to opponent
        if (socket) {
            socket.emit('timeOut', { side: side });
        }

        showGameOverPopup(`${winner} wins by timeout!`, 'timeout');
    }

});