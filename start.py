#!/usr/bin/env python3
"""
Chess Game Server Startup Script
Equivalent to 'npm start' for the Python version
"""

import subprocess
import sys
import os

def main():
    """Start the chess server"""
    try:
        print("Starting Chess Game Server...")
        print("Python version:", sys.version)
        print("Working directory:", os.getcwd())
        print("-" * 50)
        
        # Run the server
        subprocess.run([sys.executable, "server.py"], check=True)
        
    except KeyboardInterrupt:
        print("\nServer stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"Error starting server: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
