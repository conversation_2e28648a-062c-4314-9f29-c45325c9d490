const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
const path = require('path');

const app = express();
const server = http.createServer(app);
const io = new Server(server);

// Serve static files from the current directory
app.use(express.static(path.join(__dirname)));

// Serve chess.html as the default page
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'chess.html'));
});

// Game rooms storage
const gameRooms = {};

// Global waiting queue for matchmaking
let waitingPlayers = [];

// Socket.IO connection handling
io.on('connection', (socket) => {
  console.log('A user connected:', socket.id);  
  
  // Join the global waiting queue
  socket.on('joinGame', () => {
    // Check if player is already in waiting queue
    const existingPlayerIndex = waitingPlayers.findIndex(p => p.id === socket.id);
    if (existingPlayerIndex !== -1) {
      return; // Player already in queue
    }
    
    // Add player to waiting queue
    waitingPlayers.push({ id: socket.id, ready: true });
    socket.inWaitingQueue = true;
    
    console.log(`Player joined waiting queue: ${socket.id}`);
    
    // Broadcast updated waiting players list to all waiting players
    io.emit('waitingPlayersUpdate', { players: waitingPlayers });
    
    // Check if we have at least 2 players to start a game
    if (waitingPlayers.length >= 2) {
      // Take the first two players
      const player1 = waitingPlayers[0];
      const player2 = waitingPlayers[1];
      
      // Create a new game room
      const roomId = generateRoomId();
      gameRooms[roomId] = {
        players: [
          { id: player1.id, color: 'w', name: 'White Player' },
          { id: player2.id, color: 'b', name: 'Black Player' }
        ],
        currentPlayer: 0, // White starts first
        gameStarted: true,
        gameEnded: false,
        fen: 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1' // Starting position
      };
      
      // Get socket objects for both players
      const socket1 = io.sockets.sockets.get(player1.id);
      const socket2 = io.sockets.sockets.get(player2.id);
      
      if (socket1 && socket2) {
        // Join both players to the room
        socket1.join(roomId);
        socket2.join(roomId);
        
        // Set room ID for both players
        socket1.roomId = roomId;
        socket2.roomId = roomId;
        
        // Remove inWaitingQueue flag
        socket1.inWaitingQueue = false;
        socket2.inWaitingQueue = false;
        
        // Remove players from waiting queue
        waitingPlayers = waitingPlayers.filter(p => 
          p.id !== player1.id && p.id !== player2.id
        );
        
        // Broadcast updated waiting players list
        io.emit('waitingPlayersUpdate', { players: waitingPlayers });
        
        // Start the game for both players
        io.to(roomId).emit('gameStart', { 
          currentPlayer: 0,
          players: gameRooms[roomId].players
        });
        
        console.log(`Chess game started in room: ${roomId} with players: ${player1.id} (White) and ${player2.id} (Black)`);
      }
    }
  });

  // Player makes a move
  socket.on('makeMove', (data) => {
    const roomId = socket.roomId;
    const room = gameRooms[roomId];
    
    if (!room || !room.gameStarted || room.gameEnded) return;
    
    const playerIndex = room.players.findIndex(p => p.id === socket.id);
    
    // Check if it's this player's turn
    if (playerIndex !== room.currentPlayer) {
      socket.emit('error', { message: 'Not your turn' });
      return;
    }
    
    // Update game state
    room.fen = data.fen;
    
    // Switch to next player
    room.currentPlayer = room.currentPlayer === 0 ? 1 : 0;
    
    // Send move to opponent
    socket.to(roomId).emit('moveReceived', {
      move: data.move,
      fen: data.fen,
      currentPlayer: room.currentPlayer
    });
    
    console.log(`Move made in room ${roomId}: ${data.move.san}`);
  });

  // Player resigns
  socket.on('resign', () => {
    const roomId = socket.roomId;
    const room = gameRooms[roomId];

    if (!room || !room.gameStarted || room.gameEnded) return;

    room.gameEnded = true;

    // Notify opponent
    socket.to(roomId).emit('opponentResigned');

    console.log(`Player resigned in room: ${roomId}`);
  });

  // Player runs out of time
  socket.on('timeOut', (data) => {
    const roomId = socket.roomId;
    const room = gameRooms[roomId];

    if (!room || !room.gameStarted || room.gameEnded) return;

    room.gameEnded = true;

    // Notify opponent
    socket.to(roomId).emit('opponentTimeOut', { side: data.side });

    console.log(`Player timed out in room: ${roomId}, side: ${data.side}`);
  });

  // Player disconnects
  socket.on('disconnect', () => {
    console.log('User disconnected:', socket.id);
    
    // Check if player was in waiting queue
    if (socket.inWaitingQueue) {
      // Remove player from waiting queue
      waitingPlayers = waitingPlayers.filter(p => p.id !== socket.id);
      
      // Broadcast updated waiting players list
      io.emit('waitingPlayersUpdate', { players: waitingPlayers });
      
      console.log(`Player removed from waiting queue: ${socket.id}`);
      return;
    }
    
    // Handle disconnect from game room
    const roomId = socket.roomId;
    if (!roomId || !gameRooms[roomId]) return;
    
    const room = gameRooms[roomId];
    
    // Notify opponent about disconnection
    socket.to(roomId).emit('opponentDisconnected');
    
    // End the game
    if (!room.gameEnded) {
      room.gameEnded = true;
    }
    
    // Clean up room after a delay
    setTimeout(() => {
      delete gameRooms[roomId];
      console.log(`Room deleted: ${roomId}`);
    }, 30000); // 30 seconds delay
  });
});

// Generate a random room ID
function generateRoomId() {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

// Start the server
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Chess server running on port ${PORT}`);
});
