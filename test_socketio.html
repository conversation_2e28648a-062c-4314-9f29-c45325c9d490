<!DOCTYPE html>
<html>
<head>
    <title>Socket.IO Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
</head>
<body>
    <h1>Socket.IO Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="messages"></div>
    
    <script>
        const socket = io();
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');
        
        function addMessage(msg) {
            const p = document.createElement('p');
            p.textContent = new Date().toLocaleTimeString() + ': ' + msg;
            messagesDiv.appendChild(p);
        }
        
        socket.on('connect', () => {
            statusDiv.textContent = 'Connected!';
            statusDiv.style.color = 'green';
            addMessage('Connected to server');
            console.log('Connected to server');
        });
        
        socket.on('disconnect', () => {
            statusDiv.textContent = 'Disconnected';
            statusDiv.style.color = 'red';
            addMessage('Disconnected from server');
            console.log('Disconnected from server');
        });
        
        socket.on('connected', (data) => {
            addMessage('Server says: ' + data.data);
            console.log('Server response:', data);
        });
        
        socket.on('connect_error', (error) => {
            statusDiv.textContent = 'Connection Error';
            statusDiv.style.color = 'red';
            addMessage('Connection error: ' + error.message);
            console.error('Connection error:', error);
        });
    </script>
</body>
</html>
