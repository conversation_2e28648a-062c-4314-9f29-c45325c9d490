# Chess Game - Python Backend

A real-time multiplayer chess game with a Python Flask-SocketIO backend and JavaScript frontend.

## Features

- Real-time multiplayer chess gameplay
- Automatic player matchmaking
- Move validation and game state management
- Support for all chess rules including castling, en passant, and promotion
- Player disconnection handling
- Resignation and timeout functionality

## Technology Stack

### Backend (Python)
- **Flask**: Web framework for serving static files and API endpoints
- **Flask-SocketIO**: Real-time bidirectional communication between server and clients
- **Python 3.7+**: Required Python version

### Frontend (JavaScript)
- **Chess.js**: Chess game logic and move validation
- **Chessboard.js**: Interactive chess board UI
- **Socket.IO Client**: Real-time communication with the server
- **jQuery**: DOM manipulation and utilities

## Installation

### Prerequisites
- Python 3.7 or higher
- pip (Python package installer)

### Setup

1. **Clone or download the project**
   ```bash
   cd chess-main
   ```

2. **Install Python dependencies**
   ```bash
   pip install -r requirements.txt
   ```

## Running the Server

### Option 1: Direct Python execution
```bash
python server.py
```

### Option 2: Using the startup script (Cross-platform)
```bash
python start.py
```

### Option 3: Using batch file (Windows)
```bash
start.bat
```

The server will start on `http://localhost:3000` by default.

## Environment Variables

- `PORT`: Server port (default: 3000)

Example:
```bash
PORT=8080 python server.py
```

## Game Rules

The game follows standard chess rules:
- Standard piece movement and capture rules
- Castling (kingside and queenside)
- En passant capture
- Pawn promotion
- Check and checkmate detection
- Stalemate detection

## API Endpoints

### HTTP Routes
- `GET /` - Serves the main chess game page
- `GET /<filename>` - Serves static files (CSS, JS, etc.)

### Socket.IO Events

#### Client to Server
- `joinGame` - Join the matchmaking queue
- `makeMove` - Send a chess move
- `resign` - Resign from the current game
- `timeOut` - Report a timeout

#### Server to Client
- `waitingPlayersUpdate` - Update on players in queue
- `gameStart` - Game has started with player assignments
- `moveReceived` - Opponent's move received
- `opponentResigned` - Opponent has resigned
- `opponentTimeOut` - Opponent has timed out
- `opponentDisconnected` - Opponent has disconnected
- `error` - Error message

## Development

### Project Structure
```
chess-main/
├── server.py              # Main Python server file
├── requirements.txt       # Python dependencies
├── start.py              # Cross-platform startup script
├── start.bat             # Windows batch startup script
├── chess.html            # Main HTML file
├── chess.css             # Styles
├── chess.js              # Frontend JavaScript logic
├── package.json          # Original Node.js config (legacy)
├── server.js             # Original Node.js server (legacy)
└── README.md             # This file
```

### Legacy Files
The following files are from the original Node.js implementation and are kept for reference:
- `package.json`
- `package-lock.json`
- `server.js`

## Troubleshooting

### Common Issues

1. **Port already in use**
   - Change the port using the `PORT` environment variable
   - Kill any existing processes using the port

2. **Module not found errors**
   - Ensure all dependencies are installed: `pip install -r requirements.txt`
   - Check Python version: `python --version`

3. **Connection issues**
   - Ensure the server is running
   - Check firewall settings
   - Verify the correct URL is being used

### Debug Mode
The server runs in debug mode by default, which provides:
- Automatic reloading on code changes
- Detailed error messages
- Debug console output

To disable debug mode, modify `server.py`:
```python
socketio.run(app, host='0.0.0.0', port=PORT, debug=False)
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.
